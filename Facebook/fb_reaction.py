import asyncio
import logging
import random
import string
import aiohttp
import requests
import re
import json
import time
import urllib3
from urllib.parse import urlparse
from Proxy.proxies import Proxy

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
total_data = []
total_errors = []


class FacebookReactionAnalyzer:
    def __init__(self, post_url, proxy, type_get):
        self.post_url = post_url
        self.post_id = post_url.split("_")[1] if "_" in post_url else post_url
        self.proxy_url = proxy
        self.type_get = type_get
        self.api_url = "https://www.facebook.com/api/graphql/"
        self.api_headers = {
            'content-type': 'application/x-www-form-urlencoded',
            'origin': 'https://www.facebook.com',
            'sec-ch-prefers-color-scheme': 'dark',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-full-version-list': '"Chromium";v="136.0.7103.113", "Google Chrome";v="136.0.7103.113", "Not.A/Brand";v="99.0.0.0"',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'x-asbd-id': '359341',
        }
        self.base_payload = {
            "fb_api_caller_class": "RelayModern",
            "server_timestamps": "true",
        }
        self.data_reaction = {}

    async def _fetch_feedback_data(self,
                                   doc_id: str,
                                   friendly_name: str,
                                   key_path: list[str]
                                   ) -> int:
        payload = {
            **self.base_payload,
            'doc_id': doc_id,
            'fb_api_req_friendly_name': friendly_name,
            'variables': json.dumps({"feedbackTargetID": self.post_id}),
        }

        headers = {
            **self.api_headers,
            'x-fb-friendly-name': friendly_name,
        }

        connector = aiohttp.TCPConnector(ssl=False)
        async with aiohttp.ClientSession(connector=connector) as session:
            try:
                async with session.post(
                        url=self.api_url,
                        headers=headers,
                        data=payload,
                        proxy=self.proxy_url,
                        timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    html = await response.text()
                    data = json.loads(html)
                    node = data.get('data', {}).get('feedback', {})

                    for key in key_path:
                        if node is None:
                            return 0
                        node = node.get(key, {})

                    return node if isinstance(node, int) else node.get('count', 0) or 0
            except Exception as e:
                print(f"❌ Error fetching {friendly_name}: {e}")
                return None

    async def get_share_count(self) -> int:
        return await self._fetch_feedback_data(
            doc_id="9843821265734688",
            friendly_name="CometUFISharesCountTooltipContentQuery",
            key_path=["reshares"]
        )
    async def get_cmt_count(self) -> int:
        return await self._fetch_feedback_data(
            doc_id="23985348781089875",
            friendly_name="CometUFICommentsCountTooltipContentQuery",
            key_path=["comment_rendering_instance", "comments", "total_count"]
        )
    async def get_total_reaction(self) -> int:
        return await self._fetch_feedback_data(
            doc_id="9516958201714477",
            friendly_name="CometUFIReactionIconTooltipContentQuery",
            key_path=["reactors"]
        )
    async def analyze_reactions(self):
        totalLike = 0
        self.data_reaction["post_id"] = self.post_id
        tasks = []
        if "reaction" in self.type_get:
            tasks.append(asyncio.create_task(self.get_total_reaction()))
        if "comment" in self.type_get:
            tasks.append(asyncio.create_task(self.get_cmt_count()))
        if "share" in self.type_get:
            tasks.append(asyncio.create_task(self.get_share_count()))
            
        task_data = await asyncio.gather(*tasks)
        if "reaction" in self.type_get:
            self.data_reaction["reaction_count"] = task_data[0]
        if "comment" in self.type_get:
            self.data_reaction["comment_count"] = task_data[1]
        if "share" in self.type_get:
            self.data_reaction["share_count"] = task_data[2]
        return self.data_reaction


def chunkify(lst, n):
    for i in range(0, len(lst), n):
        yield lst[i:i + n]


async def run_tasks(urls,type_get, semaphore):
    start_time = time.time()

    proxies = Proxy()
    prx = await proxies.getProxy('page')
    proxy = random.choice(prx)
    proxy_url = f"http://{proxy['proxy']}"
    print(f"🌐 Proxy đang dùng: {proxy_url}")

    async def safe_analyze(url):
        async with semaphore:
            analyzer = FacebookReactionAnalyzer(url, proxy_url,type_get)
            return await analyzer.analyze_reactions()

    tasks = [asyncio.create_task(safe_analyze(url)) for url in urls]
    data = await asyncio.gather(*tasks)
    end_time = time.time()
    elapsed_time = end_time - start_time

    print(f"\n✅ Tổng thời gian run task: {elapsed_time:.2f} giây")
    print(f"Total data success: {len(total_data)}")
    print(f"Total data error: {len(total_errors)}")
    prx = await proxies.getProxy('test')
    proxy = random.choice(prx)
    new_prx = f"http://{proxy['proxy']}"
    if elapsed_time < 60 and proxy_url == new_prx:
        sleep_time = 60 - elapsed_time
        print(f"⏳ Task chạy quá nhanh, chờ thêm {sleep_time:.2f} giây...")
        time.sleep(sleep_time)
    return data


async def run(urls, type_get):
    print(f"🔗 Đã đọc {len(urls)} URL từ file")
    if len(urls) <= 0:
        print("No data ")
        return []
    start_time = time.time()
    semaphore = asyncio.Semaphore(200)
    chunk_size = 200
    all_chunks = list(chunkify(urls, chunk_size))
    for i, chunk in enumerate(all_chunks, 1):
        print(f"\n🚀 Đang xử lý chunk {i}/{len(all_chunks)} (số lượng: {len(chunk)})")
        await run_tasks(chunk,type_get, semaphore)

    while total_errors:
        # copy và reset danh sách lỗi
        retry_urls = total_errors.copy()
        total_errors.clear()
        retry_chunks = list(chunkify(retry_urls, chunk_size))
        for i, chunk in enumerate(retry_chunks, 1):
            print(f"\n🔁 Đang retry chunk {i}/{len(retry_chunks)}")
            await run_tasks(chunk,type_get, semaphore)

    end_time = time.time()
    print(f"\n✅ Tổng thời gian: {end_time - start_time:.2f} giây")
    print(f"📊 Tổng URL: {len(urls)}")
    print(f"✅ Thành công: {len(total_data)}")
    print(f"❌ Thất bại: {len(total_errors)}")
    return total_data


if __name__ == "__main__":
    data = [
        "1129802682496042_1247150707143223",
        "6641138239336272_9873018926148171",
        "1129802682496042_1219237896357927",
        "100006928900548_3970389879868603",
        "718035684104586"
    ]
    asyncio.run(run(data,"reaction,comment,share"))
